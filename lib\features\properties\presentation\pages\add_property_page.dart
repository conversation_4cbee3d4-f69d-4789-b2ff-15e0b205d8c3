import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/models/property_model.dart';
import '../../../../core/services/image_service.dart';
import '../../../../core/utils/validators.dart';
import '../../../auth/providers/auth_provider.dart';
import '../../providers/properties_provider.dart';

class AddPropertyPage extends ConsumerStatefulWidget {
  const AddPropertyPage({super.key});

  @override
  ConsumerState<AddPropertyPage> createState() => _AddPropertyPageState();
}

class _AddPropertyPageState extends ConsumerState<AddPropertyPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _areaController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _districtController = TextEditingController();

  PropertyType _selectedType = PropertyType.apartment;
  PropertyStatus _selectedStatus = PropertyStatus.forSale;
  String _selectedCurrency = 'YER';
  int _bedrooms = 1;
  int _bathrooms = 1;
  bool _hasParking = false;
  bool _hasElevator = false;
  bool _hasGarden = false;
  bool _hasPool = false;
  List<File> _selectedImages = [];

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _areaController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _districtController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final propertiesState = ref.watch(propertiesProvider);

    if (!authState.isAuthenticated) {
      return Scaffold(
        appBar: AppBar(
          title: const Text(AppStrings.addProperty),
          centerTitle: true,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.lock_outline,
                size: 80,
                color: Colors.grey,
              ),
              SizedBox(height: 24),
              Text(
                'تسجيل الدخول مطلوب',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 16),
              Text(
                'يجب تسجيل الدخول لإضافة عقار جديد',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.addProperty),
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: propertiesState.isLoading ? null : _submitProperty,
            child: Text(
              'نشر',
              style: TextStyle(
                color: propertiesState.isLoading ? Colors.grey : Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Property Images Section
              _buildImageSection(),
              const SizedBox(height: 24),

              // Basic Information
              _buildBasicInfoSection(),
              const SizedBox(height: 24),

              // Property Details
              _buildDetailsSection(),
              const SizedBox(height: 24),

              // Location
              _buildLocationSection(),
              const SizedBox(height: 24),

              // Features
              _buildFeaturesSection(),
              const SizedBox(height: 32),

              // Submit Button
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: propertiesState.isLoading ? null : _submitProperty,
                  child: propertiesState.isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('نشر العقار'),
                ),
              ),

              const SizedBox(height: 16),

              // Note
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                  border: Border.all(color: Colors.blue.withOpacity(0.3)),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue),
                    SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'سيتم مراجعة العقار من قبل المشرفين قبل النشر',
                        style: TextStyle(
                          color: Colors.blue,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'صور العقار',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 12),
        _selectedImages.isEmpty
            ? Container(
                height: 120,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: InkWell(
                  onTap: () {
                    _showImagePickerDialog(context);
                  },
                  borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.add_photo_alternate, size: 40, color: Colors.grey),
                      SizedBox(height: 8),
                      Text(
                        'إضافة صور العقار',
                        style: TextStyle(color: Colors.grey),
                      ),
                      Text(
                        'يمكنك إضافة حتى 10 صور',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              )
            : Column(
                children: [
                  // Images grid
                  Container(
                    height: 200,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: GridView.builder(
                      padding: const EdgeInsets.all(8),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                      ),
                      itemCount: _selectedImages.length + 1,
                      itemBuilder: (context, index) {
                        if (index == _selectedImages.length) {
                          // Add more button
                          return InkWell(
                            onTap: () => _showImagePickerDialog(context),
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.grey[200],
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.grey[300]!),
                              ),
                              child: const Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.add, color: Colors.grey),
                                  Text(
                                    'إضافة',
                                    style: TextStyle(fontSize: 12, color: Colors.grey),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }

                        // Image thumbnail
                        return Stack(
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                image: DecorationImage(
                                  image: FileImage(_selectedImages[index]),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                            Positioned(
                              top: 4,
                              right: 4,
                              child: InkWell(
                                onTap: () => _removeImage(index),
                                child: Container(
                                  padding: const EdgeInsets.all(2),
                                  decoration: const BoxDecoration(
                                    color: Colors.red,
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.close,
                                    size: 16,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${_selectedImages.length}/10 صور',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
      ],
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المعلومات الأساسية',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 16),

        // Title
        TextFormField(
          controller: _titleController,
          validator: (value) => Validators.validateRequired(value, 'عنوان العقار'),
          decoration: const InputDecoration(
            labelText: 'عنوان العقار',
            hintText: 'مثال: شقة فاخرة في حي الصافية',
          ),
        ),
        const SizedBox(height: 16),

        // Description
        TextFormField(
          controller: _descriptionController,
          validator: (value) => Validators.validateRequired(value, 'وصف العقار'),
          maxLines: 4,
          decoration: const InputDecoration(
            labelText: 'وصف العقار',
            hintText: 'اكتب وصفاً تفصيلياً للعقار...',
            alignLabelWithHint: true,
          ),
        ),
        const SizedBox(height: 16),

        // Property Type
        DropdownButtonFormField<PropertyType>(
          value: _selectedType,
          decoration: const InputDecoration(
            labelText: 'نوع العقار',
          ),
          items: PropertyType.values.map((type) {
            return DropdownMenuItem(
              value: type,
              child: Text(type.displayName),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedType = value!;
            });
          },
        ),
        const SizedBox(height: 16),

        // Property Status
        DropdownButtonFormField<PropertyStatus>(
          value: _selectedStatus,
          decoration: const InputDecoration(
            labelText: 'حالة العقار',
          ),
          items: [PropertyStatus.forSale, PropertyStatus.forRent].map((status) {
            return DropdownMenuItem(
              value: status,
              child: Text(status.displayName),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedStatus = value!;
            });
          },
        ),
      ],
    );
  }

  Widget _buildDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تفاصيل العقار',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 16),

        // Price and Currency
        Row(
          children: [
            Expanded(
              flex: 3,
              child: TextFormField(
                controller: _priceController,
                validator: Validators.validatePrice,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'السعر',
                  hintText: '150000',
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedCurrency,
                decoration: const InputDecoration(
                  labelText: 'العملة',
                ),
                items: const [
                  DropdownMenuItem(value: 'YER', child: Text('ر.ي')),
                  DropdownMenuItem(value: 'USD', child: Text('دولار')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedCurrency = value!;
                  });
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Area
        TextFormField(
          controller: _areaController,
          validator: Validators.validateArea,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'المساحة (م²)',
            hintText: '120',
          ),
        ),
        const SizedBox(height: 16),

        // Bedrooms and Bathrooms
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'عدد الغرف',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        IconButton(
                          onPressed: _bedrooms > 0 ? () {
                            setState(() {
                              _bedrooms--;
                            });
                          } : null,
                          icon: const Icon(Icons.remove),
                        ),
                        Text(
                          '$_bedrooms',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              _bedrooms++;
                            });
                          },
                          icon: const Icon(Icons.add),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'عدد الحمامات',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        IconButton(
                          onPressed: _bathrooms > 0 ? () {
                            setState(() {
                              _bathrooms--;
                            });
                          } : null,
                          icon: const Icon(Icons.remove),
                        ),
                        Text(
                          '$_bathrooms',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              _bathrooms++;
                            });
                          },
                          icon: const Icon(Icons.add),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLocationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الموقع',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 16),

        // City and District
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _cityController,
                validator: (value) => Validators.validateRequired(value, 'المدينة'),
                decoration: const InputDecoration(
                  labelText: 'المدينة',
                  hintText: 'صنعاء',
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _districtController,
                validator: (value) => Validators.validateRequired(value, 'الحي'),
                decoration: const InputDecoration(
                  labelText: 'الحي',
                  hintText: 'الصافية',
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Address
        TextFormField(
          controller: _addressController,
          validator: (value) => Validators.validateRequired(value, 'العنوان'),
          decoration: const InputDecoration(
            labelText: 'العنوان التفصيلي',
            hintText: 'شارع الستين، بجانب مسجد النور',
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المميزات',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 16),

        // Features Checkboxes
        CheckboxListTile(
          title: const Text('موقف سيارة'),
          value: _hasParking,
          onChanged: (value) {
            setState(() {
              _hasParking = value ?? false;
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
        ),
        CheckboxListTile(
          title: const Text('مصعد'),
          value: _hasElevator,
          onChanged: (value) {
            setState(() {
              _hasElevator = value ?? false;
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
        ),
        CheckboxListTile(
          title: const Text('حديقة'),
          value: _hasGarden,
          onChanged: (value) {
            setState(() {
              _hasGarden = value ?? false;
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
        ),
        CheckboxListTile(
          title: const Text('مسبح'),
          value: _hasPool,
          onChanged: (value) {
            setState(() {
              _hasPool = value ?? false;
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
        ),
      ],
    );
  }

  Future<void> _submitProperty() async {
    if (!_formKey.currentState!.validate()) return;

    final user = ref.read(authProvider).user;
    if (user == null) return;

    final property = PropertyModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim(),
      type: _selectedType,
      status: _selectedStatus,
      price: double.parse(_priceController.text),
      currency: _selectedCurrency,
      area: double.parse(_areaController.text),
      bedrooms: _bedrooms,
      bathrooms: _bathrooms,
      hasParking: _hasParking,
      hasElevator: _hasElevator,
      hasGarden: _hasGarden,
      hasPool: _hasPool,
      images: _selectedImages.map((file) => file.path).toList(),
      location: LocationModel(
        address: _addressController.text.trim(),
        city: _cityController.text.trim(),
        district: _districtController.text.trim(),
      ),
      ownerId: user.id,
      owner: user,
      createdAt: DateTime.now(),
      features: _getSelectedFeatures(),
    );

    final success = await ref.read(propertiesProvider.notifier).addProperty(property);

    if (mounted && success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إضافة العقار بنجاح'),
          backgroundColor: Colors.green,
        ),
      );

      // Clear form
      _clearForm();
    }
  }

  List<String> _getSelectedFeatures() {
    final features = <String>[];

    if (_hasParking) features.add('موقف سيارة');
    if (_hasElevator) features.add('مصعد');
    if (_hasGarden) features.add('حديقة');
    if (_hasPool) features.add('مسبح');

    return features;
  }

  void _clearForm() {
    _titleController.clear();
    _descriptionController.clear();
    _priceController.clear();
    _areaController.clear();
    _addressController.clear();
    _cityController.clear();
    _districtController.clear();

    setState(() {
      _selectedType = PropertyType.apartment;
      _selectedStatus = PropertyStatus.forSale;
      _selectedCurrency = 'YER';
      _bedrooms = 1;
      _bathrooms = 1;
      _hasParking = false;
      _hasElevator = false;
      _hasGarden = false;
      _hasPool = false;
      _selectedImages.clear();
    });
  }

  void _showImagePickerDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('اختيار من المعرض'),
              subtitle: const Text('اختر صور متعددة'),
              onTap: () {
                Navigator.pop(context);
                _pickMultipleImages();
              },
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('التقاط صورة'),
              subtitle: const Text('التقط صورة جديدة'),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromCamera();
              },
            ),
            if (_selectedImages.isNotEmpty)
              ListTile(
                leading: const Icon(Icons.delete),
                title: const Text('حذف جميع الصور'),
                subtitle: Text('${_selectedImages.length} صورة محددة'),
                onTap: () {
                  Navigator.pop(context);
                  _clearAllImages();
                },
              ),
            ListTile(
              leading: const Icon(Icons.cancel),
              title: const Text('إلغاء'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickMultipleImages() async {
    final images = await ImageService.pickMultipleImages(maxImages: 10);
    if (images.isNotEmpty && mounted) {
      setState(() {
        _selectedImages.addAll(images);
        // Limit to 10 images max
        if (_selectedImages.length > 10) {
          _selectedImages = _selectedImages.take(10).toList();
        }
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إضافة ${images.length} صورة'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  Future<void> _pickImageFromCamera() async {
    final image = await ImageService.pickImage(source: ImageSource.camera);
    if (image != null && mounted) {
      setState(() {
        _selectedImages.add(image);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة الصورة'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  void _clearAllImages() {
    setState(() {
      _selectedImages.clear();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم حذف جميع الصور'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }
}
