import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../auth/providers/auth_provider.dart';

class ProfilePage extends ConsumerWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);
    final user = authState.user;

    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.account),
        centerTitle: true,
      ),
      body: user == null
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.person_outline,
                    size: 80,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 24),
                  Text(
                    'غير مسجل الدخول',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                children: [
                  // Profile Header
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                    ),
                    child: Column(
                      children: [
                        // Avatar
                        CircleAvatar(
                          radius: 40,
                          backgroundColor: Theme.of(context).primaryColor,
                          child: user.avatar != null
                              ? ClipOval(
                                  child: Image.network(
                                    user.avatar!,
                                    width: 80,
                                    height: 80,
                                    fit: BoxFit.cover,
                                  ),
                                )
                              : Text(
                                  user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                                  style: const TextStyle(
                                    fontSize: 32,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                        ),
                        const SizedBox(height: 16),

                        // Name
                        Text(
                          user.name,
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 4),

                        // Email
                        Text(
                          user.email,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),

                        // Verification Badge
                        if (user.isVerified) ...[
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.green,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.verified, size: 16, color: Colors.white),
                                SizedBox(width: 4),
                                Text(
                                  'موثق',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Menu Items
                  _buildMenuItem(
                    context,
                    icon: Icons.edit,
                    title: 'تعديل الملف الشخصي',
                    onTap: () {
                      // TODO: Navigate to edit profile
                    },
                  ),

                  _buildMenuItem(
                    context,
                    icon: Icons.home_work,
                    title: 'عقاراتي',
                    onTap: () {
                      context.push('/my-properties');
                    },
                  ),

                  _buildMenuItem(
                    context,
                    icon: Icons.favorite,
                    title: 'المفضلة',
                    onTap: () {
                      // Navigate to favorites using router
                      context.go('/home'); // This will show the main page
                      // User can then tap on favorites tab
                    },
                  ),

                  _buildMenuItem(
                    context,
                    icon: Icons.notifications,
                    title: 'الإشعارات',
                    onTap: () {
                      context.push('/notifications');
                    },
                  ),

                  _buildMenuItem(
                    context,
                    icon: Icons.settings,
                    title: 'الإعدادات',
                    onTap: () {
                      context.push('/settings');
                    },
                  ),

                  _buildMenuItem(
                    context,
                    icon: Icons.help_outline,
                    title: 'المساعدة والدعم',
                    onTap: () {
                      // TODO: Navigate to help
                    },
                  ),

                  _buildMenuItem(
                    context,
                    icon: Icons.info_outline,
                    title: 'حول التطبيق',
                    onTap: () {
                      _showAboutDialog(context);
                    },
                  ),

                  const SizedBox(height: 16),

                  // Logout Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () async {
                        await ref.read(authProvider.notifier).logout();
                        if (context.mounted) {
                          context.go('/');
                        }
                      },
                      icon: const Icon(Icons.logout),
                      label: const Text('تسجيل الخروج'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(icon, color: Theme.of(context).primaryColor),
        title: Text(title),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'دليلك السكني',
      applicationVersion: '1.0.0',
      applicationIcon: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Icon(
          Icons.home,
          color: Colors.white,
          size: 32,
        ),
      ),
      children: const [
        Text('تطبيق شامل للبحث عن العقارات في اليمن'),
        SizedBox(height: 16),
        Text('المطور: فريق دليلك السكني'),
        Text('الإصدار: 1.0.0'),
        Text('تاريخ الإصدار: أغسطس 2025'),
      ],
    );
  }
}
