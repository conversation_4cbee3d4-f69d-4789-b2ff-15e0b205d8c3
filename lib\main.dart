import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'core/router/app_router.dart';
import 'core/theme/app_theme_fallback.dart';

void main() {
  runApp(
    const ProviderScope(
      child: RealEstateApp(),
    ),
  );
}

class RealEstateApp extends StatelessWidget {
  const RealEstateApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'دليلك السكني',
      debugShowCheckedModeBanner: false,

      // Themes - Use fallback theme to avoid Google Fonts network issues
      theme: AppThemeFallback.lightTheme,
      darkTheme: AppThemeFallback.darkTheme,
      themeMode: ThemeMode.system,

      // Localization
      locale: const Locale('ar', 'SA'),
      supportedLocales: const [
        Locale('ar', 'SA'), // Arabic
        Locale('en', 'US'), // English
      ],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],

      // Router

      routerConfig: AppRouter.router,
    );
  }
}
