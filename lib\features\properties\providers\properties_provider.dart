import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/property_model.dart';
import '../../../core/models/user_model.dart';

// Properties State
class PropertiesState {
  final List<PropertyModel> properties;
  final List<PropertyModel> filteredProperties;
  final List<PropertyModel> favoriteProperties;
  final PropertyModel? selectedProperty;
  final bool isLoading;
  final String? error;
  final String searchQuery;
  final PropertyFilter filter;

  const PropertiesState({
    this.properties = const [],
    this.filteredProperties = const [],
    this.favoriteProperties = const [],
    this.selectedProperty,
    this.isLoading = false,
    this.error,
    this.searchQuery = '',
    this.filter = const PropertyFilter(),
  });

  PropertiesState copyWith({
    List<PropertyModel>? properties,
    List<PropertyModel>? filteredProperties,
    List<PropertyModel>? favoriteProperties,
    PropertyModel? selectedProperty,
    bool? isLoading,
    String? error,
    String? searchQuery,
    PropertyFilter? filter,
  }) {
    return PropertiesState(
      properties: properties ?? this.properties,
      filteredProperties: filteredProperties ?? this.filteredProperties,
      favoriteProperties: favoriteProperties ?? this.favoriteProperties,
      selectedProperty: selectedProperty ?? this.selectedProperty,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      searchQuery: searchQuery ?? this.searchQuery,
      filter: filter ?? this.filter,
    );
  }
}

// Property Filter
class PropertyFilter {
  final PropertyType? type;
  final PropertyStatus? status;
  final double? minPrice;
  final double? maxPrice;
  final double? minArea;
  final double? maxArea;
  final int? bedrooms;
  final int? bathrooms;
  final String? city;
  final String? district;
  final bool? hasParking;
  final bool? hasElevator;
  final bool? hasGarden;
  final bool? hasPool;
  final bool? isVerified;
  final bool? isFeatured;

  const PropertyFilter({
    this.type,
    this.status,
    this.minPrice,
    this.maxPrice,
    this.minArea,
    this.maxArea,
    this.bedrooms,
    this.bathrooms,
    this.city,
    this.district,
    this.hasParking,
    this.hasElevator,
    this.hasGarden,
    this.hasPool,
    this.isVerified,
    this.isFeatured,
  });

  PropertyFilter copyWith({
    PropertyType? type,
    PropertyStatus? status,
    double? minPrice,
    double? maxPrice,
    double? minArea,
    double? maxArea,
    int? bedrooms,
    int? bathrooms,
    String? city,
    String? district,
    bool? hasParking,
    bool? hasElevator,
    bool? hasGarden,
    bool? hasPool,
    bool? isVerified,
    bool? isFeatured,
  }) {
    return PropertyFilter(
      type: type ?? this.type,
      status: status ?? this.status,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      minArea: minArea ?? this.minArea,
      maxArea: maxArea ?? this.maxArea,
      bedrooms: bedrooms ?? this.bedrooms,
      bathrooms: bathrooms ?? this.bathrooms,
      city: city ?? this.city,
      district: district ?? this.district,
      hasParking: hasParking ?? this.hasParking,
      hasElevator: hasElevator ?? this.hasElevator,
      hasGarden: hasGarden ?? this.hasGarden,
      hasPool: hasPool ?? this.hasPool,
      isVerified: isVerified ?? this.isVerified,
      isFeatured: isFeatured ?? this.isFeatured,
    );
  }

  bool get isEmpty {
    return type == null &&
        status == null &&
        minPrice == null &&
        maxPrice == null &&
        minArea == null &&
        maxArea == null &&
        bedrooms == null &&
        bathrooms == null &&
        city == null &&
        district == null &&
        hasParking == null &&
        hasElevator == null &&
        hasGarden == null &&
        hasPool == null &&
        isVerified == null &&
        isFeatured == null;
  }
}

// Properties Provider
final propertiesProvider = StateNotifierProvider<PropertiesNotifier, PropertiesState>((ref) {
  return PropertiesNotifier();
});

class PropertiesNotifier extends StateNotifier<PropertiesState> {
  PropertiesNotifier() : super(const PropertiesState()) {
    _loadMockData();
  }

  // Load mock data
  void _loadMockData() {
    final mockProperties = _generateMockProperties();
    state = state.copyWith(
      properties: mockProperties,
      filteredProperties: mockProperties,
    );
  }

  // Generate mock properties
  List<PropertyModel> _generateMockProperties() {
    final cities = ['صنعاء', 'عدن', 'تعز', 'الحديدة', 'إب'];
    final districts = ['الصافية', 'الحصبة', 'شعوب', 'الثورة', 'التحرير'];
    final types = PropertyType.values;
    final statuses = [PropertyStatus.forSale, PropertyStatus.forRent];

    return List.generate(20, (index) {
      final type = types[index % types.length];
      final status = statuses[index % statuses.length];
      final city = cities[index % cities.length];
      final district = districts[index % districts.length];

      return PropertyModel(
        id: 'property_$index',
        title: _getPropertyTitle(type, index),
        description: _getPropertyDescription(type, index),
        type: type,
        status: status,
        price: _getPropertyPrice(type, status, index),
        area: 50.0 + (index * 25),
        bedrooms: (index % 4) + 1,
        bathrooms: (index % 3) + 1,
        hasParking: index % 2 == 0,
        hasElevator: index % 3 == 0,
        hasGarden: index % 4 == 0,
        hasPool: index % 5 == 0,
        images: _getPropertyImages(index),
        location: LocationModel(
          address: 'شارع ${index + 1}، حي $district',
          city: city,
          district: district,
          latitude: 15.3694 + (index * 0.01),
          longitude: 44.1910 + (index * 0.01),
        ),
        ownerId: 'owner_${index % 5}',
        isVerified: index % 3 == 0,
        isFeatured: index % 7 == 0,
        createdAt: DateTime.now().subtract(Duration(days: index)),
        features: _getPropertyFeatures(index),
        views: (index + 1) * 15,
        favorites: (index + 1) * 3,
      );
    });
  }

  String _getPropertyTitle(PropertyType type, int index) {
    switch (type) {
      case PropertyType.apartment:
        return 'شقة فاخرة ${index + 1}';
      case PropertyType.villa:
        return 'فيلا راقية ${index + 1}';
      case PropertyType.house:
        return 'منزل عائلي ${index + 1}';
      case PropertyType.office:
        return 'مكتب تجاري ${index + 1}';
      case PropertyType.shop:
        return 'محل تجاري ${index + 1}';
      case PropertyType.warehouse:
        return 'مستودع ${index + 1}';
      case PropertyType.land:
        return 'قطعة أرض ${index + 1}';
    }
  }

  String _getPropertyDescription(PropertyType type, int index) {
    return 'وصف تفصيلي للعقار رقم ${index + 1} من نوع ${type.displayName}. يتميز بموقع ممتاز ومواصفات عالية الجودة.';
  }

  double _getPropertyPrice(PropertyType type, PropertyStatus status, int index) {
    double basePrice = 100000;

    switch (type) {
      case PropertyType.apartment:
        basePrice = 150000;
        break;
      case PropertyType.villa:
        basePrice = 500000;
        break;
      case PropertyType.house:
        basePrice = 300000;
        break;
      case PropertyType.office:
        basePrice = 200000;
        break;
      case PropertyType.shop:
        basePrice = 250000;
        break;
      case PropertyType.warehouse:
        basePrice = 400000;
        break;
      case PropertyType.land:
        basePrice = 80000;
        break;
    }

    if (status == PropertyStatus.forRent) {
      basePrice = basePrice * 0.1; // 10% of sale price for rent
    }

    return basePrice + (index * 10000);
  }

  List<String> _getPropertyImages(int index) {
    return [
      'https://via.placeholder.com/400x300/2196F3/FFFFFF?text=Property+${index + 1}',
      'https://via.placeholder.com/400x300/4CAF50/FFFFFF?text=Interior+${index + 1}',
      'https://via.placeholder.com/400x300/FF9800/FFFFFF?text=Exterior+${index + 1}',
    ];
  }

  List<String> _getPropertyFeatures(int index) {
    final allFeatures = [
      'مكيف هواء',
      'تدفئة مركزية',
      'إنترنت عالي السرعة',
      'أمان 24/7',
      'قريب من المدارس',
      'قريب من المستشفيات',
      'قريب من المراكز التجارية',
      'إطلالة رائعة',
      'تشطيب فاخر',
      'مطبخ مجهز',
    ];

    return allFeatures.take((index % 5) + 3).toList();
  }

  // Search properties
  void searchProperties(String query) {
    state = state.copyWith(searchQuery: query);
    _applyFilters();
  }

  // Apply filters
  void applyFilter(PropertyFilter filter) {
    state = state.copyWith(filter: filter);
    _applyFilters();
  }

  // Clear filters
  void clearFilters() {
    state = state.copyWith(
      filter: const PropertyFilter(),
      searchQuery: '',
      filteredProperties: state.properties,
    );
  }

  // Apply search and filters
  void _applyFilters() {
    var filtered = state.properties;

    // Apply search
    if (state.searchQuery.isNotEmpty) {
      filtered = filtered.where((property) {
        return property.title.toLowerCase().contains(state.searchQuery.toLowerCase()) ||
            property.description.toLowerCase().contains(state.searchQuery.toLowerCase()) ||
            property.location.city.toLowerCase().contains(state.searchQuery.toLowerCase()) ||
            property.location.district.toLowerCase().contains(state.searchQuery.toLowerCase());
      }).toList();
    }

    // Apply filters
    final filter = state.filter;

    if (filter.type != null) {
      filtered = filtered.where((p) => p.type == filter.type).toList();
    }

    if (filter.status != null) {
      filtered = filtered.where((p) => p.status == filter.status).toList();
    }

    if (filter.minPrice != null) {
      filtered = filtered.where((p) => p.price >= filter.minPrice!).toList();
    }

    if (filter.maxPrice != null) {
      filtered = filtered.where((p) => p.price <= filter.maxPrice!).toList();
    }

    if (filter.minArea != null) {
      filtered = filtered.where((p) => p.area >= filter.minArea!).toList();
    }

    if (filter.maxArea != null) {
      filtered = filtered.where((p) => p.area <= filter.maxArea!).toList();
    }

    if (filter.bedrooms != null) {
      filtered = filtered.where((p) => p.bedrooms >= filter.bedrooms!).toList();
    }

    if (filter.bathrooms != null) {
      filtered = filtered.where((p) => p.bathrooms >= filter.bathrooms!).toList();
    }

    if (filter.city != null) {
      filtered = filtered.where((p) => p.location.city == filter.city).toList();
    }

    if (filter.district != null) {
      filtered = filtered.where((p) => p.location.district == filter.district).toList();
    }

    if (filter.hasParking == true) {
      filtered = filtered.where((p) => p.hasParking).toList();
    }

    if (filter.hasElevator == true) {
      filtered = filtered.where((p) => p.hasElevator).toList();
    }

    if (filter.hasGarden == true) {
      filtered = filtered.where((p) => p.hasGarden).toList();
    }

    if (filter.hasPool == true) {
      filtered = filtered.where((p) => p.hasPool).toList();
    }

    if (filter.isVerified == true) {
      filtered = filtered.where((p) => p.isVerified).toList();
    }

    if (filter.isFeatured == true) {
      filtered = filtered.where((p) => p.isFeatured).toList();
    }

    state = state.copyWith(filteredProperties: filtered);
  }

  // Add to favorites
  void toggleFavorite(String propertyId) {
    final favorites = List<PropertyModel>.from(state.favoriteProperties);
    final property = state.properties.firstWhere((p) => p.id == propertyId);

    if (favorites.any((p) => p.id == propertyId)) {
      favorites.removeWhere((p) => p.id == propertyId);
    } else {
      favorites.add(property);
    }

    state = state.copyWith(favoriteProperties: favorites);
  }

  // Check if property is favorite
  bool isFavorite(String propertyId) {
    return state.favoriteProperties.any((p) => p.id == propertyId);
  }

  // Select property
  void selectProperty(String propertyId) {
    final property = state.properties.firstWhere((p) => p.id == propertyId);
    state = state.copyWith(selectedProperty: property);
  }

  // Add new property
  Future<bool> addProperty(PropertyModel property) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      final properties = List<PropertyModel>.from(state.properties);
      properties.insert(0, property);

      state = state.copyWith(
        properties: properties,
        isLoading: false,
      );

      _applyFilters(); // Refresh filtered list
      return true;
    } catch (e) {
      state = state.copyWith(
        error: 'خطأ في إضافة العقار',
        isLoading: false,
      );
      return false;
    }
  }

  // Update property
  Future<bool> updateProperty(PropertyModel property) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      final properties = List<PropertyModel>.from(state.properties);
      final index = properties.indexWhere((p) => p.id == property.id);

      if (index != -1) {
        properties[index] = property;

        state = state.copyWith(
          properties: properties,
          isLoading: false,
        );

        _applyFilters(); // Refresh filtered list
        return true;
      }

      return false;
    } catch (e) {
      state = state.copyWith(
        error: 'خطأ في تحديث العقار',
        isLoading: false,
      );
      return false;
    }
  }

  // Delete property
  Future<bool> deleteProperty(String propertyId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      final properties = List<PropertyModel>.from(state.properties);
      properties.removeWhere((p) => p.id == propertyId);

      // Also remove from favorites
      final favorites = List<PropertyModel>.from(state.favoriteProperties);
      favorites.removeWhere((p) => p.id == propertyId);

      state = state.copyWith(
        properties: properties,
        favoriteProperties: favorites,
        isLoading: false,
      );

      _applyFilters(); // Refresh filtered list
      return true;
    } catch (e) {
      state = state.copyWith(
        error: 'خطأ في حذف العقار',
        isLoading: false,
      );
      return false;
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  // Refresh properties
  Future<void> refreshProperties() async {
    state = state.copyWith(isLoading: true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      _loadMockData(); // Reload mock data

      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        error: 'خطأ في تحديث البيانات',
        isLoading: false,
      );
    }
  }
}
